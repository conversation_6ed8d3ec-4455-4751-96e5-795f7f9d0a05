<?php

namespace Database\Seeders;

use App\Models\Alias;
use App\Models\City;
use Illuminate\Database\Seeder;

class CityAliasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Creates aliases for all cities using their exact name if such alias doesn't exist.
     */
    public function run(): void
    {
        $totalCities = City::count();
        echo "Processing {$totalCities} cities...\n";
        
        $processedCount = 0;
        $createdCount = 0;
        $skippedCount = 0;

        City::chunk(100, function ($cities) use (&$processedCount, &$createdCount, &$skippedCount) {
            foreach ($cities as $city) {
                $processedCount++;
                
                // Check if an alias with the exact city name already exists for this city
                $existingAlias = Alias::where('name', $city->name)
                    ->where('aliasable_type', City::class)
                    ->where('aliasable_id', $city->id)
                    ->first();
                
                if ($existingAlias) {
                    $skippedCount++;
                    echo "Skipped: {$city->name} (alias already exists)\n";
                } else {
                    // Create alias with the exact city name
                    Alias::create([
                        'name' => $city->name,
                        'aliasable_type' => City::class,
                        'aliasable_id' => $city->id,
                    ]);
                    
                    $createdCount++;
                    echo "Created alias for: {$city->name}\n";
                }
            }
        });
        
        echo "\nSummary:\n";
        echo "Total cities processed: {$processedCount}\n";
        echo "Aliases created: {$createdCount}\n";
        echo "Aliases skipped (already exist): {$skippedCount}\n";
    }
}
